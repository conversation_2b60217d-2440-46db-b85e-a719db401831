import 'package:flutter/material.dart';

class PageTransitions {
  static PageRouteBuilder slideTransition(Widget page) {
    return PageRouteBuilder(
      transitionDuration: Duration.zero, // Instant transition
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return child; // No animation, just return the child directly
      },
    );
  }

  static PageRouteBuilder fadeTransition(Widget page) {
    return PageRouteBuilder(
      transitionDuration: Duration.zero, // Instant transition
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return child; // No animation, just return the child directly
      },
    );
  }
}
