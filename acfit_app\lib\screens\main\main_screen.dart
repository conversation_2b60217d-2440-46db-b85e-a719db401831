import 'package:flutter/material.dart';
import '../home/<USER>';
import '../workout/workout_screen.dart';
import '../meal/meal_plan_screen.dart';
import '../shop/shop_screen.dart';
import '../profile/profile_screen.dart';
import '../../widgets/custom_nav_bar.dart'; // Import the new custom nav bar

class MainScreen extends StatefulWidget {
  final int? initialTabIndex;

  const MainScreen({Key? key, this.initialTabIndex}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _currentIndex;

  // No animations needed for instant transitions

  @override
  void initState() {
    super.initState();
    // Set the initial tab index if provided
    _currentIndex = widget.initialTabIndex ?? 0;
  }

  // Use GlobalKeys to access the state of each screen
  final GlobalKey _homeScreenKey = GlobalKey();
  final GlobalKey _workoutScreenKey = GlobalKey();
  final GlobalKey _mealPlanScreenKey = GlobalKey();
  final GlobalKey _shopScreenKey = GlobalKey();
  final GlobalKey _profileScreenKey = GlobalKey();

  // Create screens with keys
  late final List<Widget> _screens = [
    HomeScreen(key: _homeScreenKey),
    WorkoutScreen(key: _workoutScreenKey),
    MealPlanScreen(key: _mealPlanScreenKey),
    ShopScreen(key: _shopScreenKey),
    ProfileScreen(key: _profileScreenKey),
  ];

  // Method to refresh the current screen
  void _refreshCurrentScreen() {
    // Always refresh the home screen when switching tabs
    // This ensures calorie data is always up-to-date
    Future.delayed(Duration.zero, () {
      try {
        // Safely refresh the home screen if it exists
        if (_homeScreenKey.currentWidget is HomeScreen) {
          final homeScreen = _homeScreenKey.currentWidget as HomeScreen;
          homeScreen.refreshData();
        }

        // Also refresh the workout screen if that's the current tab
        if (_currentIndex == 1 &&
            _workoutScreenKey.currentWidget is WorkoutScreen) {
          // Force refresh of workout data
          final workoutScreen =
              _workoutScreenKey.currentWidget as WorkoutScreen;
          workoutScreen.refreshData();
        }
      } catch (e) {
        print('Error refreshing screens: $e');
      }
    });
  }

  // Custom transition builder for AnimatedSwitcher - instant transition
  Widget _transitionBuilder(Widget child, Animation<double> animation) {
    return child; // No animation, just return the child directly
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedSwitcher(
        duration: Duration.zero, // Instant transition
        transitionBuilder: _transitionBuilder,
        child: KeyedSubtree(
          key: ValueKey<int>(_currentIndex),
          child: _screens[_currentIndex],
        ),
      ),
      // Replace BottomNavigationBar with CustomNavBar
      bottomNavigationBar: CustomNavBar(
        currentIndex: _currentIndex,
        onItemTapped: (index) {
          // If tapping the same tab, refresh it
          if (_currentIndex == index) {
            _refreshCurrentScreen();
          } else {
            setState(() {
              _currentIndex = index;
            });

            // Refresh the newly selected screen
            _refreshCurrentScreen();
          }
        },
      ),
      // Remove FloatingActionButton and its location
      // floatingActionButton: _currentIndex == 0
      //     ? FloatingActionButton(
      //         heroTag: 'mainFAB',
      //         onPressed: () {},
      //         backgroundColor: const Color.fromRGBO(255, 127, 54, 1),
      //         elevation: 2,
      //         child: const Icon(Icons.add, color: Colors.white),
      //       )
      //     : null,
      // floatingActionButtonLocation: null,
    );
  }
}
